<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::content}, ~{::scripts})}">
<head>
    <title>Audit Dashboard - JPA Auditing Demo</title>
</head>
<body>
    <div th:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>Audit Dashboard
                </h1>
                <p class="text-muted mb-0">Monitor and analyze system audit logs</p>
            </div>
            <div>
                <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-secondary" onclick="exportAuditLogs()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title" id="totalAuditLogs">-</h4>
                                <p class="card-text">Total Audit Logs</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title" id="createOperations">-</h4>
                                <p class="card-text">Create Operations</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-plus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title" id="updateOperations">-</h4>
                                <p class="card-text">Update Operations</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-edit fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title" id="deleteOperations">-</h4>
                                <p class="card-text">Delete Operations</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-trash fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>Filters
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="entityNameFilter" class="form-label">Entity Type</label>
                        <select class="form-select" id="entityNameFilter" name="entityName">
                            <option value="">All Entities</option>
                            <option value="User">User</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="operationFilter" class="form-label">Operation</label>
                        <select class="form-select" id="operationFilter" name="operation">
                            <option value="">All Operations</option>
                            <option value="CREATE">Create</option>
                            <option value="UPDATE">Update</option>
                            <option value="DELETE">Delete</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="startDateFilter" class="form-label">Start Date</label>
                        <input type="datetime-local" class="form-control" id="startDateFilter" name="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDateFilter" class="form-label">End Date</label>
                        <input type="datetime-local" class="form-control" id="endDateFilter" name="endDate">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Apply Filters
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Audit Logs Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Audit Logs
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="auditLogsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Timestamp</th>
                                <th>Entity</th>
                                <th>Entity ID</th>
                                <th>Operation</th>
                                <th>User</th>
                                <th>Changes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="auditLogsTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <nav aria-label="Audit logs pagination" class="mt-3">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <div th:fragment="scripts">
        <script th:inline="javascript">
            let currentPage = 0;
            const pageSize = 20;

            // Initialize dashboard
            document.addEventListener('DOMContentLoaded', function() {
                loadStatistics();
                loadAuditLogs();
                
                // Set up filter form
                document.getElementById('filterForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    currentPage = 0;
                    loadAuditLogs();
                });
            });

            // Load statistics
            function loadStatistics() {
                // Load total audit logs
                fetch(/*[[@{/api/audit}]]*/ '/api/audit?size=1')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('totalAuditLogs').textContent = data.totalElements || 0;
                    })
                    .catch(error => {
                        console.error('Error loading total audit logs:', error);
                        document.getElementById('totalAuditLogs').textContent = 'Error';
                    });

                // Load operation statistics
                ['CREATE', 'UPDATE', 'DELETE'].forEach(operation => {
                    fetch(/*[[@{/api/audit/operation/}]]*/ '/api/audit/operation/' + operation + '?size=1')
                        .then(response => response.json())
                        .then(data => {
                            const elementId = operation.toLowerCase() + 'Operations';
                            document.getElementById(elementId).textContent = data.totalElements || 0;
                        })
                        .catch(error => {
                            console.error(`Error loading ${operation} operations:`, error);
                            const elementId = operation.toLowerCase() + 'Operations';
                            document.getElementById(elementId).textContent = 'Error';
                        });
                });
            }

            // Load audit logs with filters
            function loadAuditLogs() {
                const formData = new FormData(document.getElementById('filterForm'));
                const params = new URLSearchParams();
                
                // Add pagination
                params.append('page', currentPage);
                params.append('size', pageSize);
                
                // Add filters
                for (let [key, value] of formData.entries()) {
                    if (value) {
                        params.append(key, value);
                    }
                }

                fetch(/*[[@{/api/audit}]]*/ '/api/audit?' + params.toString())
                    .then(response => response.json())
                    .then(data => {
                        displayAuditLogs(data.content || []);
                        updatePagination(data);
                    })
                    .catch(error => {
                        console.error('Error loading audit logs:', error);
                        document.getElementById('auditLogsTableBody').innerHTML = 
                            '<tr><td colspan="7" class="text-center text-danger">Error loading audit logs</td></tr>';
                    });
            }

            // Display audit logs in table
            function displayAuditLogs(auditLogs) {
                const tbody = document.getElementById('auditLogsTableBody');
                
                if (auditLogs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No audit logs found</td></tr>';
                    return;
                }

                tbody.innerHTML = auditLogs.map(log => `
                    <tr>
                        <td>${AuditDemo.formatTimestamp(log.timestamp)}</td>
                        <td><span class="badge bg-info">${log.entityName}</span></td>
                        <td><code>${log.entityId}</code></td>
                        <td><span class="badge ${AuditDemo.getOperationBadgeClass(log.operation)}">${log.operation}</span></td>
                        <td>${log.userId || 'System'}</td>
                        <td>
                            ${log.details && log.details.length > 0 ? 
                                `<span class="badge bg-secondary">${log.details.length} field(s)</span>` : 
                                '<span class="text-muted">No details</span>'
                            }
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewAuditDetails(${log.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="AuditDemo.showAuditTrail('${log.entityName}', '${log.entityId}')">
                                <i class="fas fa-history"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            // Update pagination
            function updatePagination(pageData) {
                const pagination = document.getElementById('pagination');
                const totalPages = pageData.totalPages || 0;
                
                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }

                let paginationHtml = '';
                
                // Previous button
                paginationHtml += `
                    <li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>
                    </li>
                `;
                
                // Page numbers
                for (let i = 0; i < totalPages; i++) {
                    if (i === currentPage || i === 0 || i === totalPages - 1 || Math.abs(i - currentPage) <= 2) {
                        paginationHtml += `
                            <li class="page-item ${i === currentPage ? 'active' : ''}">
                                <a class="page-link" href="#" onclick="changePage(${i})">${i + 1}</a>
                            </li>
                        `;
                    } else if (i === currentPage - 3 || i === currentPage + 3) {
                        paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                }
                
                // Next button
                paginationHtml += `
                    <li class="page-item ${currentPage >= totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>
                    </li>
                `;
                
                pagination.innerHTML = paginationHtml;
            }

            // Change page
            function changePage(page) {
                if (page >= 0) {
                    currentPage = page;
                    loadAuditLogs();
                }
            }

            // View audit details
            function viewAuditDetails(auditId) {
                fetch(/*[[@{/api/audit/}]]*/ '/api/audit/' + auditId)
                    .then(response => response.json())
                    .then(auditLog => {
                        showAuditDetailsModal(auditLog);
                    })
                    .catch(error => {
                        console.error('Error loading audit details:', error);
                        alert('Error loading audit details');
                    });
            }

            // Show audit details modal
            function showAuditDetailsModal(auditLog) {
                const modalHtml = `
                    <div class="modal fade" id="auditDetailsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Audit Log Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>Entity:</strong> ${auditLog.entityName}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Entity ID:</strong> <code>${auditLog.entityId}</code>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>Operation:</strong> 
                                            <span class="badge ${AuditDemo.getOperationBadgeClass(auditLog.operation)}">${auditLog.operation}</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>User:</strong> ${auditLog.userId || 'System'}
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <strong>Timestamp:</strong> ${AuditDemo.formatTimestamp(auditLog.timestamp)}
                                        </div>
                                    </div>
                                    ${auditLog.details && auditLog.details.length > 0 ? `
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Field Changes:</strong>
                                                <div class="table-responsive mt-2">
                                                    <table class="table table-sm table-bordered">
                                                        <thead>
                                                            <tr>
                                                                <th>Field</th>
                                                                <th>Old Value</th>
                                                                <th>New Value</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${auditLog.details.map(detail => `
                                                                <tr>
                                                                    <td><code>${detail.fieldName}</code></td>
                                                                    <td>${detail.oldValue || '<em>null</em>'}</td>
                                                                    <td>${detail.newValue || '<em>null</em>'}</td>
                                                                </tr>
                                                            `).join('')}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    ` : '<p class="text-muted">No field changes recorded</p>'}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" onclick="AuditDemo.showAuditTrail('${auditLog.entityName}', '${auditLog.entityId}')">
                                        View Full Trail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // Remove existing modal if any
                const existingModal = document.getElementById('auditDetailsModal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                // Add modal to body
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('auditDetailsModal'));
                modal.show();
            }

            // Clear filters
            function clearFilters() {
                document.getElementById('filterForm').reset();
                currentPage = 0;
                loadAuditLogs();
            }

            // Refresh dashboard
            function refreshDashboard() {
                loadStatistics();
                currentPage = 0;
                loadAuditLogs();
            }

            // Export audit logs (placeholder)
            function exportAuditLogs() {
                alert('Export functionality would be implemented here');
            }
        </script>
    </div>
</body>
</html>
