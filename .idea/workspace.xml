<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PropertiesComponent">{}</component>
  <component name="RunManager">
    <configuration name="JpaAuditingDemoApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.audit.JpaAuditingDemoApplication" />
      <module name="jpa-auditing-demo" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.audit.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.JpaAuditingDemoApplication" />
      </list>
    </recent_temporary>
  </component>
</project>